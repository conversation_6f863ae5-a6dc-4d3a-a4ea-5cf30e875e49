'use client';

import { useState } from 'react';
import { Plus, Edit, Trash2, Eye, Download } from 'lucide-react';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

import { DocumentTemplate } from '@/features/document/types';
import { CreateTemplateDialog } from './create-template-dialog';
import { EditTemplateDialog } from './edit-template-dialog';
import { DeleteTemplateDialog } from './delete-template-dialog';

interface TemplateManagementProps {
  templates: DocumentTemplate[];
  onTemplateCreated: (template: DocumentTemplate) => void;
  onTemplateUpdated: (template: DocumentTemplate) => void;
  onTemplateDeleted: (templateId: string) => void;
}

export function TemplateManagement({
  templates,
  onTemplateCreated,
  onTemplateUpdated,
  onTemplateDeleted,
}: Readonly<TemplateManagementProps>) {
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [editingTemplate, setEditingTemplate] =
    useState<DocumentTemplate | null>(null);
  const [deletingTemplate, setDeletingTemplate] =
    useState<DocumentTemplate | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const categories = Array.from(new Set(templates.map((t) => t.category)));
  const filteredTemplates =
    selectedCategory === 'all'
      ? templates
      : templates.filter((t) => t.category === selectedCategory);

  const handleCreateTemplate = (template: DocumentTemplate) => {
    onTemplateCreated(template);
    setShowCreateDialog(false);
  };

  const handleEditTemplate = (template: DocumentTemplate) => {
    onTemplateUpdated(template);
    setEditingTemplate(null);
  };

  const handleDeleteTemplate = (templateId: string) => {
    onTemplateDeleted(templateId);
    setDeletingTemplate(null);
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      Admisión: 'bg-blue-100 text-blue-800',
      Notificación: 'bg-yellow-100 text-yellow-800',
      Suspensión: 'bg-red-100 text-red-800',
      Acuerdo: 'bg-green-100 text-green-800',
      Calificación: 'bg-purple-100 text-purple-800',
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Gestión de Plantillas</h2>
          <p className="text-gray-600">
            Administre las plantillas de documentos Word para generación
            automática
          </p>
        </div>
        <Button onClick={() => setShowCreateDialog(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Nueva Plantilla
        </Button>
      </div>

      {/* Filtros */}
      <Card>
        <CardHeader>
          <CardTitle>Filtros</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button
              variant={selectedCategory === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedCategory('all')}
            >
              Todas ({templates.length})
            </Button>
            {categories.map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedCategory(category)}
              >
                {category} (
                {templates.filter((t) => t.category === category).length})
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Lista de plantillas */}
      <Card>
        <CardHeader>
          <CardTitle>Plantillas Disponibles</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Nombre</TableHead>
                <TableHead>Categoría</TableHead>
                <TableHead>Descripción</TableHead>
                <TableHead>Placeholders</TableHead>
                <TableHead>Estado</TableHead>
                <TableHead>Fecha</TableHead>
                <TableHead>Acciones</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredTemplates.map((template) => (
                <TableRow key={template.id}>
                  <TableCell className="font-medium">{template.name}</TableCell>
                  <TableCell>
                    <Badge className={getCategoryColor(template.category)}>
                      {template.category}
                    </Badge>
                  </TableCell>
                  <TableCell className="max-w-xs truncate">
                    {template.description || 'Sin descripción'}
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {template.placeholders.length} campos
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={template.isActive ? 'default' : 'secondary'}
                    >
                      {template.isActive ? 'Activa' : 'Inactiva'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {template.createdAt.toLocaleDateString('es-CO')}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          const url = `/api/templates/${template.id}/preview`;
                          window.open(url, '_blank');
                        }}
                        title="Vista previa"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          const url = `/api/templates/${template.id}/download`;
                          window.open(url, '_blank');
                        }}
                        title="Descargar"
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setEditingTemplate(template)}
                        title="Editar"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setDeletingTemplate(template)}
                        title="Eliminar"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {filteredTemplates.length === 0 && (
            <div className="py-8 text-center text-gray-500">
              No hay plantillas disponibles para la categoría seleccionada
            </div>
          )}
        </CardContent>
      </Card>

      {/* Diálogos */}
      <CreateTemplateDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        onTemplateCreated={handleCreateTemplate}
      />

      {editingTemplate && (
        <EditTemplateDialog
          template={editingTemplate}
          open={!!editingTemplate}
          onOpenChange={(open) => !open && setEditingTemplate(null)}
          onTemplateUpdated={handleEditTemplate}
        />
      )}

      <DeleteTemplateDialog
        template={deletingTemplate}
        open={!!deletingTemplate}
        onOpenChange={(open) => !open && setDeletingTemplate(null)}
        onTemplateDeleted={handleDeleteTemplate}
      />
    </div>
  );
}
