'use client';

import { <PERSON>, FileText, RefreshCw, ExternalLink } from 'lucide-react';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';

import type React from 'react';

interface DriveDocument {
  id: string;
  name: string;
  mimeType: string;
  size: string;
  modifiedTime: string;
  webViewLink: string;
  thumbnailLink?: string;
  parents: string[];
}

interface DocumentData {
  name: string;
  type: string;
  caseId: string;
  debtorName: string;
  category: string;
  status: string;
  size: string;
  format: string;
  description: string;
  tags: string[];
  content: string;
  driveId: string;
  driveLink: string;
}

interface GoogleDriveSyncDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSyncDocument: (documentData: Partial<DocumentData>) => void;
}

export function GoogleDriveSyncDialog({
  open,
  onOpenChange,
  onSyncDocument,
}: Readonly<GoogleDriveSyncDialogProps>) {
  const [formData, setFormData] = useState({
    name: '',
    type: '',
    caseId: '',
    category: '',
    description: '',
    tags: '',
  });

  const [driveDocuments, setDriveDocuments] = useState<DriveDocument[]>([]);
  const [selectedDocument, setSelectedDocument] =
    useState<DriveDocument | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isConnected, setIsConnected] = useState(false);

  const documentTypes = [
    'Auto de Admisión',
    'Notificación a Acreedores',
    'Suspensión Procesos Judiciales',
    'Acuerdo de Pago',
    'Tabla de Amortización',
    'Certificado REDAM',
    'Extractos Bancarios',
    'Cédula de Ciudadanía',
    'Poder Notariado',
    'Otro',
  ];

  const categories = [
    'Admisión',
    'Notificación',
    'Suspensión',
    'Acuerdo',
    'Cálculo',
    'Identificación',
    'Financiero',
    'Legal',
    'Otro',
  ];

  const cases = [
    { id: 'INS-2025-001', debtorName: 'María González Pérez' },
    { id: 'CON-2025-002', debtorName: 'Carlos Rodríguez Silva' },
    { id: 'ACU-2025-003', debtorName: 'Ana Martínez López' },
    { id: 'INS-2025-004', debtorName: 'Luis Fernando Castro' },
  ];

  const connectToGoogleDrive = async () => {
    setIsLoading(true);
    try {
      await new Promise((resolve) => setTimeout(resolve, 1500));
      setIsConnected(true);

      const mockDriveDocuments: DriveDocument[] = [
        {
          id: 'drive_1',
          name: 'Auto de Admisión - INS-2025-001.pdf',
          mimeType: 'application/pdf',
          size: '2.4 MB',
          modifiedTime: '2025-01-15T10:30:00Z',
          webViewLink: 'https://drive.google.com/file/d/drive_1/view',
          parents: ['folder_legal'],
        },
        {
          id: 'drive_2',
          name: 'Certificado REDAM - CON-2025-002.pdf',
          mimeType: 'application/pdf',
          size: '1.8 MB',
          modifiedTime: '2025-01-14T15:45:00Z',
          webViewLink: 'https://drive.google.com/file/d/drive_2/view',
          parents: ['folder_certificates'],
        },
        {
          id: 'drive_3',
          name: 'Extractos Bancarios - ACU-2025-003.xlsx',
          mimeType:
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          size: '3.2 MB',
          modifiedTime: '2025-01-13T09:20:00Z',
          webViewLink: 'https://drive.google.com/file/d/drive_3/view',
          parents: ['folder_financial'],
        },
      ];

      setDriveDocuments(mockDriveDocuments);
    } catch (error) {
      console.error('Error connecting to Google Drive:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatFileSize = (sizeStr: string) => {
    return sizeStr;
  };

  const formatDate = (dateStr: string) => {
    return new Date(dateStr).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const handleSubmit = () => {
    if (
      !formData.name ||
      !formData.type ||
      !formData.caseId ||
      !selectedDocument
    ) {
      alert(
        'Por favor complete todos los campos obligatorios y seleccione un documento de Google Drive',
      );
      return;
    }

    const selectedCase = cases.find((c) => c.id === formData.caseId);

    const documentData = {
      name: formData.name,
      type: formData.type,
      caseId: formData.caseId,
      debtorName: selectedCase?.debtorName ?? '',
      category: formData.category,
      status: 'Sincronizado',
      size: selectedDocument.size,
      format: selectedDocument.mimeType.includes('pdf')
        ? 'PDF'
        : selectedDocument.mimeType.includes('spreadsheet')
          ? 'XLSX'
          : 'DOCX',
      description: formData.description,
      tags: formData.tags
        .split(',')
        .map((tag) => tag.trim())
        .filter((tag) => tag),
      content: `Documento sincronizado desde Google Drive: ${selectedDocument.name}`,
      driveId: selectedDocument.id,
      driveLink: selectedDocument.webViewLink,
    };

    onSyncDocument(documentData);
    onOpenChange(false);

    setFormData({
      name: '',
      type: '',
      caseId: '',
      category: '',
      description: '',
      tags: '',
    });
    setSelectedDocument(null);

    alert('✅ Documento sincronizado exitosamente desde Google Drive');
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-3xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Sincronizar con Google Drive</DialogTitle>
          <DialogDescription>
            Sincronice documentos desde Google Drive al expediente de un caso
            específico
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          <Card>
            <CardContent className="p-6">
              {!isConnected ? (
                <div className="rounded-lg border-2 border-dashed p-8 text-center">
                  <Cloud className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                  <p className="mb-2 text-lg font-medium">
                    Conectar con Google Drive
                  </p>
                  <p className="mb-4 text-sm text-gray-600">
                    Acceda a sus documentos almacenados en Google Drive
                  </p>
                  <Button
                    onClick={connectToGoogleDrive}
                    disabled={isLoading}
                    className="min-w-[200px]"
                  >
                    {isLoading ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        Conectando...
                      </>
                    ) : (
                      <>
                        <Cloud className="mr-2 h-4 w-4" />
                        Conectar a Drive
                      </>
                    )}
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium text-green-600">
                      ✅ Conectado a Google Drive
                    </h3>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={connectToGoogleDrive}
                    >
                      <RefreshCw className="mr-2 h-4 w-4" />
                      Actualizar
                    </Button>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium">Documentos disponibles:</h4>
                    <div className="max-h-60 space-y-2 overflow-y-auto">
                      {driveDocuments.map((doc) => (
                        <div
                          key={doc.id}
                          className={`cursor-pointer rounded-lg border p-3 transition-colors ${
                            selectedDocument?.id === doc.id
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                          onClick={() => setSelectedDocument(doc)}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <FileText className="h-5 w-5 text-blue-600" />
                              <div>
                                <p className="font-medium">{doc.name}</p>
                                <p className="text-sm text-gray-600">
                                  {formatFileSize(doc.size)} •{' '}
                                  {formatDate(doc.modifiedTime)}
                                </p>
                              </div>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                window.open(doc.webViewLink, '_blank');
                              }}
                            >
                              <ExternalLink className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="name">Nombre del Documento *</Label>
              <Input
                id="name"
                placeholder="Ej: Auto de Admisión - INS-2025-001"
                value={formData.name}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, name: e.target.value }))
                }
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="type">Tipo de Documento *</Label>
              <Select
                value={formData.type}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, type: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Seleccionar tipo" />
                </SelectTrigger>
                <SelectContent>
                  {documentTypes.map((type) => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="caseId">Caso Asociado *</Label>
              <Select
                value={formData.caseId}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, caseId: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Seleccionar caso" />
                </SelectTrigger>
                <SelectContent>
                  {cases.map((case_) => (
                    <SelectItem key={case_.id} value={case_.id}>
                      {case_.id} - {case_.debtorName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="category">Categoría</Label>
              <Select
                value={formData.category}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, category: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Seleccionar categoría" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Descripción</Label>
            <Textarea
              id="description"
              placeholder="Descripción opcional del documento..."
              value={formData.description}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  description: e.target.value,
                }))
              }
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="tags">Etiquetas</Label>
            <Input
              id="tags"
              placeholder="Separar con comas: urgente, revision, firmado"
              value={formData.tags}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, tags: e.target.value }))
              }
            />
          </div>
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <DialogClose asChild>
            <Button variant="outline">Cancelar</Button>
          </DialogClose>
          <Button
            onClick={handleSubmit}
            disabled={!selectedDocument || !isConnected}
          >
            <Cloud className="mr-2 h-4 w-4" />
            Sincronizar Documento
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
