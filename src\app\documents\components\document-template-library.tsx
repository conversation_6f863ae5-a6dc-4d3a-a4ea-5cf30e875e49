'use client';

import {
  FileText,
  Search,
  Eye,
  Download,
  Edit,
  Plus,
  Trash2,
} from 'lucide-react';
import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';

import { DocumentTemplate } from '@/features/document/types';
import { CreateTemplateDialog } from './create-template-dialog';
import { EditTemplateDialog } from './edit-template-dialog';
import { DeleteTemplateDialog } from './delete-template-dialog';

interface DocumentTemplateLibraryProps {
  templates: DocumentTemplate[];
  onTemplateCreated: (template: DocumentTemplate) => void;
  onTemplateUpdated: (template: DocumentTemplate) => void;
  onTemplateDeleted: (templateId: string) => void;
}

export function DocumentTemplateLibrary({
  templates,
  onTemplateCreated,
  onTemplateUpdated,
  onTemplateDeleted,
}: Readonly<DocumentTemplateLibraryProps>) {
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [editingTemplate, setEditingTemplate] =
    useState<DocumentTemplate | null>(null);
  const [deletingTemplate, setDeletingTemplate] =
    useState<DocumentTemplate | null>(null);

  const handleCreateTemplate = (template: DocumentTemplate) => {
    onTemplateCreated(template);
    setShowCreateDialog(false);
  };

  const handleEditTemplate = (template: DocumentTemplate) => {
    onTemplateUpdated(template);
    setEditingTemplate(null);
  };

  const handleDeleteTemplate = (templateId: string) => {
    onTemplateDeleted(templateId);
    setDeletingTemplate(null);
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Inicial':
        return 'bg-blue-100 text-blue-800';
      case 'Admisión':
        return 'bg-green-100 text-green-800';
      case 'Notificación':
        return 'bg-yellow-100 text-yellow-800';
      case 'Suspensión':
        return 'bg-red-100 text-red-800';
      case 'Calificación':
        return 'bg-purple-100 text-purple-800';
      case 'Acuerdo':
        return 'bg-emerald-100 text-emerald-800';
      case 'Incumplimiento':
        return 'bg-orange-100 text-orange-800';
      case 'Reforma':
        return 'bg-indigo-100 text-indigo-800';
      case 'Cierre':
        return 'bg-gray-100 text-gray-800';
      case 'Fracaso':
        return 'bg-red-100 text-red-800';
      case 'Finalización':
        return 'bg-teal-100 text-teal-800';
      case 'Control':
        return 'bg-cyan-100 text-cyan-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredTemplates = templates.filter((template) => {
    const matchesSearch =
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (template.description?.toLowerCase().includes(searchTerm.toLowerCase()) ??
        false);
    const matchesCategory =
      categoryFilter === 'all' || template.category === categoryFilter;
    return matchesSearch && matchesCategory;
  });

  const categories = [...new Set(templates.map((t) => t.category))];

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Biblioteca de Plantillas</CardTitle>
              <CardDescription>
                Plantillas de documentos legales disponibles en el sistema
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <div className="relative">
                <Search className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar plantillas..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-80 pl-10"
                />
              </div>
              <Button onClick={() => setShowCreateDialog(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Nueva Plantilla
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Filtros por categoría */}
          <div className="mb-6 flex flex-wrap gap-2">
            <Button
              variant={categoryFilter === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setCategoryFilter('all')}
            >
              Todas ({templates.length})
            </Button>
            {categories.map((category) => (
              <Button
                key={category}
                variant={categoryFilter === category ? 'default' : 'outline'}
                size="sm"
                onClick={() => setCategoryFilter(category)}
              >
                {category} (
                {templates.filter((t) => t.category === category).length})
              </Button>
            ))}
          </div>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            {filteredTemplates.map((template) => (
              <Card
                key={template.id}
                className="transition-shadow hover:shadow-md"
              >
                <CardContent className="p-6">
                  <div className="mb-3 flex items-start justify-between">
                    <div className="flex items-center space-x-2">
                      <FileText className="h-5 w-5 text-blue-600" />
                      <Badge className={getCategoryColor(template.category)}>
                        {template.category}
                      </Badge>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline">
                        {template.placeholders.length} campos
                      </Badge>
                      <Badge
                        variant={template.isActive ? 'default' : 'secondary'}
                      >
                        {template.isActive ? 'Activa' : 'Inactiva'}
                      </Badge>
                    </div>
                  </div>

                  <h3 className="mb-2 font-semibold">{template.name}</h3>
                  <p className="mb-4 text-sm text-gray-600">
                    {template.description || 'Sin descripción'}
                  </p>

                  <div className="mb-4 flex items-center justify-between text-xs text-gray-500">
                    <span>
                      Actualizado:{' '}
                      {template.updatedAt.toLocaleDateString('es-CO')}
                    </span>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      onClick={() => {
                        const url = `/api/templates/${template.id}/preview`;
                        window.open(url, '_blank');
                      }}
                    >
                      <Eye className="mr-2 h-4 w-4" />
                      Vista Previa
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const url = `/api/templates/${template.id}/download`;
                        window.open(url, '_blank');
                      }}
                      title="Descargar"
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setEditingTemplate(template)}
                      title="Editar"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setDeletingTemplate(template)}
                      title="Eliminar"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Plantillas Activas
                </p>
                <p className="text-lg font-bold">
                  {templates.filter((t) => t.isActive).length}
                </p>
                <p className="text-sm text-gray-500">
                  De {templates.length} totales
                </p>
              </div>
              <FileText className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Categorías</p>
                <p className="text-lg font-bold">{categories.length}</p>
                <p className="text-sm text-gray-500">Tipos de documentos</p>
              </div>
              <FileText className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Total Plantillas
                </p>
                <p className="text-lg font-bold">{templates.length}</p>
                <p className="text-sm text-gray-500">
                  Disponibles en el sistema
                </p>
              </div>
              <FileText className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Diálogos */}
      <CreateTemplateDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        onTemplateCreated={handleCreateTemplate}
      />

      {editingTemplate && (
        <EditTemplateDialog
          template={editingTemplate}
          open={!!editingTemplate}
          onOpenChange={(open) => !open && setEditingTemplate(null)}
          onTemplateUpdated={handleEditTemplate}
        />
      )}

      <DeleteTemplateDialog
        template={deletingTemplate}
        open={!!deletingTemplate}
        onOpenChange={(open) => !open && setDeletingTemplate(null)}
        onTemplateDeleted={handleDeleteTemplate}
      />
    </div>
  );
}
