'use server';

import { revalidatePath } from 'next/cache';
import { createServerAction } from 'zsa';
import { z } from 'zod';

import prisma from '@/lib/prisma';

import {
  createDocumentSchema,
  updateDocumentSchema,
  deleteDocumentSchema,
  documentFilterSchema,
  documentStatsSchema,
  getDocumentsSchema,
  getDocumentByIdSchema,
  createDocumentOutputSchema,
  updateDocumentOutputSchema,
  deleteDocumentOutputSchema,
  createDocumentTemplateSchema,
  generateDocumentFromTemplateSchema,
  updateDocumentContentSchema,
  documentTemplateSchema,
} from './schemas';

export const getDocuments = createServerAction()
  .input(documentFilterSchema.optional())
  .output(getDocumentsSchema)
  .handler(async ({ input: filters }) => {
    return prisma.document.findMany({
      where: {
        ...(filters?.caseId && { caseId: filters.caseId }),
        ...(filters?.type && { type: filters.type }),
        ...(filters?.status && { status: filters.status }),
        ...(filters?.search && {
          OR: [
            { name: { contains: filters.search, mode: 'insensitive' } },
            {
              case: {
                caseNumber: { contains: filters.search, mode: 'insensitive' },
              },
            },
            {
              case: {
                debtorName: { contains: filters.search, mode: 'insensitive' },
              },
            },
          ],
        }),
      },
      include: {
        case: {
          select: {
            id: true,
            caseNumber: true,
            debtorName: true,
            type: true,
            status: true,
          },
        },
      },
      orderBy: {
        uploadDate: 'desc',
      },
    });
  });

export const getDocumentById = createServerAction()
  .input(
    z.object({ id: z.string().min(1, 'El ID del documento es requerido') }),
  )
  .output(getDocumentByIdSchema)
  .handler(async ({ input: { id } }) => {
    const document = await prisma.document.findUnique({
      where: { id },
      include: {
        case: {
          select: {
            id: true,
            caseNumber: true,
            debtorName: true,
            type: true,
            status: true,
          },
        },
      },
    });

    if (!document) {
      throw new Error('Documento no encontrado');
    }

    return document;
  });

export const createDocument = createServerAction()
  .input(createDocumentSchema)
  .output(createDocumentOutputSchema)
  .handler(async ({ input: data }) => {
    const document = await prisma.document.create({
      data: {
        ...data,
        status: data.status ?? 'PENDIENTE',
      },
      include: {
        case: {
          select: {
            id: true,
            caseNumber: true,
            debtorName: true,
            type: true,
            status: true,
          },
        },
      },
    });

    revalidatePath('/documents');
    return document;
  });

export const updateDocument = createServerAction()
  .input(updateDocumentSchema)
  .output(updateDocumentOutputSchema)
  .handler(async ({ input: data }) => {
    const { id: documentId, ...updateData } = data;
    const document = await prisma.document.update({
      where: { id: documentId },
      data: updateData,
      include: {
        case: {
          select: {
            id: true,
            caseNumber: true,
            debtorName: true,
            type: true,
            status: true,
          },
        },
      },
    });

    revalidatePath('/documents');
    return document;
  });

export const deleteDocument = createServerAction()
  .input(deleteDocumentSchema)
  .output(deleteDocumentOutputSchema)
  .handler(async ({ input: { id } }) => {
    const deletedDocument = await prisma.document.delete({
      where: { id },
    });

    revalidatePath('/documents');
    return deletedDocument;
  });

// Acciones para plantillas de documentos
export const getDocumentTemplates = createServerAction()
  .input(z.object({ category: z.string().optional() }).optional())
  .output(z.array(documentTemplateSchema))
  .handler(async ({ input }) => {
    const templates = await prisma.documentTemplate.findMany({
      where: {
        isActive: true,
        ...(input?.category && { category: input.category }),
      },
      orderBy: {
        name: 'asc',
      },
    });

    return templates.map((template) => ({
      ...template,
      description: template.description || undefined,
      placeholders: Array.isArray(template.placeholders)
        ? (template.placeholders as Array<{
            type: 'text' | 'number' | 'date' | 'boolean';
            required: boolean;
            key: string;
            label: string;
            description?: string;
            defaultValue?: string;
          }>)
        : [],
    }));
  });

export const createDocumentTemplate = createServerAction()
  .input(createDocumentTemplateSchema)
  .output(documentTemplateSchema)
  .handler(async ({ input: data }) => {
    const { googleDriveService } = await import('@/lib/google-drive');

    // Convertir el archivo a Buffer para subirlo a Google Drive
    const fileBuffer = data.file
      ? Buffer.from(await data.file.arrayBuffer())
      : Buffer.from('');

    if (!data.file) {
      throw new Error('El archivo es requerido');
    }

    // Subir archivo a Google Drive
    const googleDriveId = await googleDriveService.uploadFile(
      data.file.name,
      fileBuffer,
      data.file.type,
    );

    const template = await prisma.documentTemplate.create({
      data: {
        name: data.name,
        description: data.description,
        category: data.category,
        googleDriveId,
        fileName: data.file.name,
        mimeType: data.file.type,
        placeholders: data.placeholders,
      },
    });

    revalidatePath('/documents/templates');
    return {
      ...template,
      description: template.description || undefined,
      placeholders: Array.isArray(template.placeholders)
        ? (template.placeholders as Array<{
            type: 'text' | 'number' | 'date' | 'boolean';
            required: boolean;
            key: string;
            label: string;
            description?: string;
            defaultValue?: string;
          }>)
        : [],
    };
  });

export const updateDocumentTemplate = createServerAction()
  .input(
    z.object({
      id: z.string().min(1, 'El ID de la plantilla es requerido'),
      name: z.string().min(1, 'El nombre es requerido'),
      description: z.string().optional(),
      category: z.string().min(1, 'La categoría es requerida'),
      placeholders: z
        .array(
          z.object({
            key: z.string().min(1, 'La clave es requerida'),
            label: z.string().min(1, 'La etiqueta es requerida'),
            type: z.enum(['text', 'number', 'date', 'email']),
            required: z.boolean(),
            description: z.string().optional(),
          }),
        )
        .optional(),
    }),
  )
  .output(documentTemplateSchema)
  .handler(async ({ input: data }) => {
    const { id, ...updateData } = data;

    const template = await prisma.documentTemplate.update({
      where: { id },
      data: updateData,
    });

    revalidatePath('/documents/templates');
    return {
      ...template,
      description: template.description || undefined,
      placeholders: Array.isArray(template.placeholders)
        ? (template.placeholders as Array<{
            type: 'text' | 'number' | 'date' | 'boolean';
            required: boolean;
            key: string;
            label: string;
            description?: string;
            defaultValue?: string;
          }>)
        : [],
    };
  });

export const downloadDocumentTemplate = createServerAction()
  .input(
    z.object({ id: z.string().min(1, 'El ID de la plantilla es requerido') }),
  )
  .output(
    z.object({
      buffer: z.any(),
      fileName: z.string(),
      mimeType: z.string(),
    }),
  )
  .handler(async ({ input: { id } }) => {
    const { googleDriveService } = await import('@/lib/google-drive');

    const template = await prisma.documentTemplate.findUnique({
      where: { id },
    });

    if (!template) {
      throw new Error('Plantilla no encontrada');
    }

    const buffer = await googleDriveService.downloadFile(
      template.googleDriveId,
    );

    return {
      buffer,
      fileName: template.fileName,
      mimeType: template.mimeType,
    };
  });

export const deleteDocumentTemplate = createServerAction()
  .input(
    z.object({ id: z.string().min(1, 'El ID de la plantilla es requerido') }),
  )
  .output(z.object({ success: z.boolean() }))
  .handler(async ({ input: { id } }) => {
    const { googleDriveService } = await import('@/lib/google-drive');

    // Obtener la plantilla para conseguir el ID de Google Drive
    const template = await prisma.documentTemplate.findUnique({
      where: { id },
    });

    if (!template) {
      throw new Error('Plantilla no encontrada');
    }

    // Eliminar archivo de Google Drive
    try {
      await googleDriveService.deleteFile(template.googleDriveId);
    } catch (error) {
      console.error('Error eliminando archivo de Google Drive:', error);
      // Continuar con la eliminación de la base de datos aunque falle Google Drive
    }

    // Eliminar de la base de datos
    await prisma.documentTemplate.update({
      where: { id },
      data: { isActive: false },
    });

    revalidatePath('/documents/templates');
    return { success: true };
  });

export const generateDocumentFromTemplate = createServerAction()
  .input(generateDocumentFromTemplateSchema)
  .output(createDocumentOutputSchema)
  .handler(async ({ input: data }) => {
    // Obtener la plantilla
    const template = await prisma.documentTemplate.findUnique({
      where: { id: data.templateId },
    });

    if (!template) {
      throw new Error('Plantilla no encontrada');
    }

    // Obtener datos del caso para el contexto
    const caseData = await prisma.case.findUnique({
      where: { id: data.caseId },
      include: {
        debtor: true,
        operator: true,
      },
    });

    if (!caseData) {
      throw new Error('Caso no encontrado');
    }

    // Aquí se implementaría la lógica de generación del documento
    // usando la biblioteca docx-templates para reemplazar placeholders

    const document = await prisma.document.create({
      data: {
        name: data.name,
        type: template.category,
        status: 'GENERADO',
        url: '', // Se generaría la URL después de guardar el archivo
        caseId: data.caseId,
        templateId: data.templateId,
        isGenerated: true,
      },
      include: {
        case: {
          select: {
            id: true,
            caseNumber: true,
            debtorName: true,
            type: true,
            status: true,
          },
        },
      },
    });

    revalidatePath('/documents');
    return document;
  });

export const updateDocumentContent = createServerAction()
  .input(updateDocumentContentSchema)
  .output(z.object({ success: z.boolean() }))
  .handler(async ({ input: data }) => {
    // Obtener el documento actual
    const currentDocument = await prisma.document.findUnique({
      where: { id: data.documentId },
    });

    if (!currentDocument) {
      throw new Error('Documento no encontrado');
    }

    // For now, we'll just update the document name or status
    // In the future, this could upload a new version to Google Drive
    await prisma.document.update({
      where: { id: data.documentId },
      data: {
        name: data.content.substring(0, 100), // Use first 100 chars as name
        status: 'ACTUALIZADO',
      },
    });

    revalidatePath('/documents');
    return { success: true };
  });

export const getDocumentStats = createServerAction()
  .output(documentStatsSchema)
  .handler(async () => {
    const [total, byStatus, byType] = await Promise.all([
      prisma.document.count(),
      prisma.document.groupBy({
        by: ['status'],
        _count: {
          id: true,
        },
      }),
      prisma.document.groupBy({
        by: ['type'],
        _count: {
          id: true,
        },
      }),
    ]);

    return {
      total,
      byStatus,
      byType,
    };
  });
