'use client';

import { useState, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useServerAction } from 'zsa-react';
import { toast } from 'sonner';
import { Download, FileText, Eye } from 'lucide-react';
import {
  DocumentTemplate,
  PlaceholderDefinition,
} from '@/features/document/types';
import {
  createDocumentTemplateSchema,
  CreateDocumentTemplateData,
} from '@/features/document/schemas';
import { updateDocumentTemplate } from '@/features/document/actions';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Trash2, Plus, Loader2 } from 'lucide-react';

interface EditTemplateDialogProps {
  template: DocumentTemplate;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onTemplateUpdated: (template: DocumentTemplate) => void;
}

export function EditTemplateDialog({
  template,
  open,
  onOpenChange,
  onTemplateUpdated,
}: Readonly<EditTemplateDialogProps>) {
  const closeRef = useRef<HTMLButtonElement>(null);
  const [placeholders, setPlaceholders] = useState<PlaceholderDefinition[]>(
    template.placeholders || [],
  );

  const form = useForm({
    resolver: zodResolver(createDocumentTemplateSchema),
    defaultValues: {
      name: template.name,
      description: template.description || '',
      category: template.category,
      placeholders: template.placeholders || [],
    },
  });

  const { execute: executeUpdate, isPending } = useServerAction(
    updateDocumentTemplate,
    {
      onSuccess: ({ data }) => {
        toast.success('Plantilla actualizada exitosamente');
        onTemplateUpdated(data);
        closeRef.current?.click();
      },
      onError: ({ err }) => {
        toast.error(err.message || 'Error al actualizar la plantilla');
      },
    },
  );

  const addPlaceholder = () => {
    const newPlaceholder: PlaceholderDefinition = {
      key: '',
      label: '',
      type: 'text',
      required: false,
    };
    setPlaceholders([...placeholders, newPlaceholder]);
    form.setValue('placeholders', [...placeholders, newPlaceholder]);
  };

  const removePlaceholder = (index: number) => {
    const newPlaceholders = placeholders.filter((_, i) => i !== index);
    setPlaceholders(newPlaceholders);
    form.setValue('placeholders', newPlaceholders);
  };

  const updatePlaceholder = (
    index: number,
    field: keyof PlaceholderDefinition,
    value: string | boolean,
  ) => {
    const newPlaceholders = [...placeholders];
    newPlaceholders[index] = { ...newPlaceholders[index], [field]: value };
    setPlaceholders(newPlaceholders);
    form.setValue('placeholders', newPlaceholders);
  };

  const onSubmit = (data: unknown) => {
    const formData = data as CreateDocumentTemplateData;
    const transformedPlaceholders = (formData.placeholders || [])
      .filter((p) => p.type !== 'boolean')
      .map((p) => ({
        key: p.key,
        label: p.label,
        type: p.type as 'text' | 'number' | 'date' | 'email',
        required: p.required || false,
        description: p.description,
      }));

    executeUpdate({
      id: template.id,
      name: formData.name,
      description: formData.description,
      category: formData.category,
      placeholders: transformedPlaceholders,
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Editar Plantilla</DialogTitle>
          <DialogDescription>
            Modifica los detalles de la plantilla: {template.name}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Sección de visualización del documento */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Documento de Plantilla
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between rounded-lg border p-4">
                  <div className="flex items-center gap-3">
                    <div className="rounded-lg bg-blue-100 p-2">
                      <FileText className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-medium">{template.fileName}</p>
                      <p className="text-sm text-gray-500">
                        Archivo Word de plantilla
                      </p>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const url = `/api/templates/${template.id}/preview`;
                        window.open(url, '_blank');
                      }}
                    >
                      <Eye className="mr-2 h-4 w-4" />
                      Vista Previa
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const url = `/api/templates/${template.id}/download`;
                        window.open(url, '_blank');
                      }}
                    >
                      <Download className="mr-2 h-4 w-4" />
                      Descargar
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nombre de la Plantilla</FormLabel>
                    <FormControl>
                      <Input placeholder="Nombre de la plantilla" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Categoría</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecciona una categoría" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Notificación">
                          Notificación
                        </SelectItem>
                        <SelectItem value="Contrato">Contrato</SelectItem>
                        <SelectItem value="Informe">Informe</SelectItem>
                        <SelectItem value="Carta">Carta</SelectItem>
                        <SelectItem value="Otro">Otro</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Descripción</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Descripción de la plantilla"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Placeholders</h3>
                <Button type="button" onClick={addPlaceholder} size="sm">
                  <Plus className="mr-2 h-4 w-4" />
                  Agregar Placeholder
                </Button>
              </div>

              {placeholders.map((placeholder, index) => (
                <Card key={index}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-sm">
                        Placeholder {index + 1}
                      </CardTitle>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removePlaceholder(index)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                      <div>
                        <label className="text-sm font-medium">Clave</label>
                        <Input
                          placeholder="ej: nombre_cliente"
                          value={placeholder.key}
                          onChange={(e) =>
                            updatePlaceholder(index, 'key', e.target.value)
                          }
                        />
                      </div>
                      <div>
                        <label className="text-sm font-medium">Etiqueta</label>
                        <Input
                          placeholder="ej: Nombre del Cliente"
                          value={placeholder.label}
                          onChange={(e) =>
                            updatePlaceholder(index, 'label', e.target.value)
                          }
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                      <div>
                        <label className="text-sm font-medium">Tipo</label>
                        <Select
                          value={placeholder.type}
                          onValueChange={(value) =>
                            updatePlaceholder(index, 'type', value)
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="text">Texto</SelectItem>
                            <SelectItem value="number">Número</SelectItem>
                            <SelectItem value="date">Fecha</SelectItem>
                            <SelectItem value="email">Email</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="flex items-center space-x-2 pt-6">
                        <input
                          type="checkbox"
                          id={`required-${index}`}
                          checked={placeholder.required}
                          onChange={(e) =>
                            updatePlaceholder(
                              index,
                              'required',
                              e.target.checked,
                            )
                          }
                        />
                        <label
                          htmlFor={`required-${index}`}
                          className="text-sm font-medium"
                        >
                          Requerido
                        </label>
                      </div>
                    </div>
                    {placeholder.description && (
                      <div>
                        <label className="text-sm font-medium">
                          Descripción
                        </label>
                        <Textarea
                          placeholder="Descripción del placeholder"
                          value={placeholder.description}
                          onChange={(e) =>
                            updatePlaceholder(
                              index,
                              'description',
                              e.target.value,
                            )
                          }
                          className="resize-none"
                        />
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>

            <div className="flex justify-end space-x-2">
              <DialogClose asChild>
                <Button
                  type="button"
                  variant="outline"
                  ref={closeRef}
                  disabled={isPending}
                >
                  Cancelar
                </Button>
              </DialogClose>
              <Button type="submit" disabled={isPending}>
                {isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Actualizar Plantilla
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
