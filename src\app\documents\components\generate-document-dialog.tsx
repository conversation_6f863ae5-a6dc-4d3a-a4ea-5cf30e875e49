'use client';

import { useState, useEffect, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useServerAction } from 'zsa-react';
import { toast } from 'sonner';
import { FileText, Download, Loader2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import {
  generateDocumentFromTemplateSchema,
  GenerateDocumentFromTemplateData,
} from '@/features/document/schemas';
import {
  DocumentTemplate,
  PlaceholderDefinition,
} from '@/features/document/types';
import { generateDocumentFromTemplate } from '@/features/document/actions';

interface GenerateDocumentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  templates: DocumentTemplate[];
  cases: Array<{
    id: string;
    caseNumber: string;
    debtorName: string;
    type: string;
  }>;
  onDocumentGenerated: (document: unknown) => void;
}

export function GenerateDocumentDialog({
  open,
  onOpenChange,
  templates,
  cases,
  onDocumentGenerated,
}: Readonly<GenerateDocumentDialogProps>) {
  const closeRef = useRef<HTMLButtonElement>(null);
  const [selectedTemplate, setSelectedTemplate] =
    useState<DocumentTemplate | null>(null);

  const { execute: executeGenerate, isPending } = useServerAction(
    generateDocumentFromTemplate,
    {
      onSuccess: ({ data }) => {
        toast.success('Documento generado exitosamente');
        onDocumentGenerated(data);
        closeRef.current?.click();
      },
      onError: ({ err }) => {
        toast.error(err.message || 'Error al generar el documento');
      },
    },
  );

  const form = useForm<GenerateDocumentFromTemplateData>({
    resolver: zodResolver(generateDocumentFromTemplateSchema),
    defaultValues: {
      templateId: '',
      caseId: '',
      name: '',
      placeholderValues: {},
    },
  });

  const templateId = form.watch('templateId');
  const caseId = form.watch('caseId');

  useEffect(() => {
    if (templateId) {
      const template = templates.find((t) => t.id === templateId);
      setSelectedTemplate(template || null);

      if (template && caseId) {
        const selectedCase = cases.find((c) => c.id === caseId);
        if (selectedCase) {
          const autoName = `${template.name} - ${selectedCase.caseNumber}`;
          form.setValue('name', autoName);
        }
      }
    }
  }, [templateId, caseId, templates, cases, form]);

  const handlePlaceholderChange = (key: string, value: string) => {
    const currentValues = form.getValues('placeholderValues');
    form.setValue('placeholderValues', {
      ...currentValues,
      [key]: value,
    });
  };

  const onSubmit = (data: GenerateDocumentFromTemplateData) => {
    executeGenerate(data);
  };

  const renderPlaceholderInput = (placeholder: PlaceholderDefinition) => {
    const currentValue =
      form.getValues('placeholderValues')[placeholder.key] || '';

    switch (placeholder.type) {
      case 'number':
        return (
          <Input
            type="number"
            value={currentValue}
            onChange={(e) =>
              handlePlaceholderChange(placeholder.key, e.target.value)
            }
            placeholder={placeholder.defaultValue}
          />
        );
      case 'date':
        return (
          <Input
            type="date"
            value={currentValue}
            onChange={(e) =>
              handlePlaceholderChange(placeholder.key, e.target.value)
            }
          />
        );
      case 'boolean':
        return (
          <Select
            value={currentValue}
            onValueChange={(value) =>
              handlePlaceholderChange(placeholder.key, value)
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Seleccione..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">Sí</SelectItem>
              <SelectItem value="false">No</SelectItem>
            </SelectContent>
          </Select>
        );
      default:
        return (
          <Input
            value={currentValue}
            onChange={(e) =>
              handlePlaceholderChange(placeholder.key, e.target.value)
            }
            placeholder={placeholder.defaultValue}
          />
        );
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-[1000px]">
        <DialogHeader>
          <DialogTitle>Generar Documento desde Plantilla</DialogTitle>
          <DialogDescription>
            Seleccione una plantilla y complete los campos para generar un nuevo
            documento
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="templateId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Plantilla</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Seleccione una plantilla" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {templates.map((template) => (
                          <SelectItem key={template.id} value={template.id}>
                            <div className="flex items-center space-x-2">
                              <span>{template.name}</span>
                              <Badge variant="outline" className="text-xs">
                                {template.category}
                              </Badge>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="caseId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Caso</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Seleccione un caso" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {cases.map((case_) => (
                          <SelectItem key={case_.id} value={case_.id}>
                            {case_.caseNumber} - {case_.debtorName}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nombre del Documento</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Nombre del documento generado"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {selectedTemplate && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <FileText className="h-5 w-5" />
                    <span>{selectedTemplate.name}</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm text-gray-600">
                        {selectedTemplate.description}
                      </p>
                      <div className="mt-2 flex items-center space-x-2">
                        <Badge>{selectedTemplate.category}</Badge>
                        <Badge variant="outline">
                          {selectedTemplate.placeholders.length} campos
                        </Badge>
                      </div>
                    </div>

                    {selectedTemplate.placeholders.length > 0 && (
                      <div className="space-y-4">
                        <h4 className="font-medium">Campos del Documento</h4>
                        <div className="grid grid-cols-2 gap-4">
                          {selectedTemplate.placeholders.map((placeholder) => (
                            <div key={placeholder.key} className="space-y-2">
                              <label className="flex items-center space-x-1 text-sm font-medium">
                                <span>{placeholder.label}</span>
                                {placeholder.required && (
                                  <span className="text-red-500">*</span>
                                )}
                              </label>
                              {renderPlaceholderInput(placeholder)}
                              <p className="text-xs text-gray-500">
                                Placeholder: {`{{${placeholder.key}}}`}
                              </p>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            <div className="flex justify-end space-x-3">
              <DialogClose asChild>
                <Button
                  type="button"
                  variant="outline"
                  ref={closeRef}
                  disabled={isPending}
                >
                  Cancelar
                </Button>
              </DialogClose>
              <Button
                type="submit"
                disabled={!selectedTemplate || isPending}
                className="flex items-center space-x-2"
              >
                {isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>Generando...</span>
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4" />
                    <span>Generar Documento</span>
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
