import prisma from '@/lib/prisma';
import { DocumentVersion } from '@/features/document/types';

export class DocumentVersionManager {
  /**
   * Crea una nueva versión de un documento
   */
  static async createVersion(
    documentId: string,
    content: string,
    googleDriveId?: string,
    fileName?: string,
    mimeType?: string,
    changes?: string,
    createdBy: string = 'system',
  ): Promise<DocumentVersion> {
    // Obtener el documento actual
    const currentDocument = await prisma.document.findUnique({
      where: { id: documentId },
    });

    if (!currentDocument) {
      throw new Error('Documento no encontrado');
    }

    // Crear nueva versión con el contenido anterior
    const newVersion = await prisma.documentVersion.create({
      data: {
        documentId,
        version: currentDocument.version,
        content: currentDocument.content,
        googleDriveId: currentDocument.googleDriveId,
        fileName: currentDocument.fileName,
        mimeType: currentDocument.mimeType,
        changes: changes || 'Versión guardada automáticamente',
        createdBy,
      },
    });

    // Actualizar el documento con el nuevo contenido
    await prisma.document.update({
      where: { id: documentId },
      data: {
        content,
        googleDriveId,
        fileName,
        mimeType,
        version: currentDocument.version + 1,
      },
    });

    return {
      id: newVersion.id,
      documentId: newVersion.documentId,
      version: newVersion.version,
      content: newVersion.content || undefined,
      changes: newVersion.changes || undefined,
      createdAt: newVersion.createdAt,
      createdBy: newVersion.createdBy,
    };
  }

  /**
   * Obtiene el historial de versiones de un documento
   */
  static async getVersionHistory(
    documentId: string,
  ): Promise<DocumentVersion[]> {
    const versions = await prisma.documentVersion.findMany({
      where: { documentId },
      orderBy: { version: 'desc' },
    });

    return versions.map((version) => ({
      id: version.id,
      documentId: version.documentId,
      version: version.version,
      content: version.content || undefined,
      changes: version.changes || undefined,
      createdAt: version.createdAt,
      createdBy: version.createdBy,
    }));
  }

  /**
   * Restaura una versión específica de un documento
   */
  static async restoreVersion(
    documentId: string,
    versionId: string,
    restoredBy: string = 'system',
  ): Promise<void> {
    // Obtener la versión a restaurar
    const versionToRestore = await prisma.documentVersion.findUnique({
      where: { id: versionId },
    });

    if (!versionToRestore || versionToRestore.documentId !== documentId) {
      throw new Error('Versión no encontrada');
    }

    // Obtener el documento actual
    const currentDocument = await prisma.document.findUnique({
      where: { id: documentId },
    });

    if (!currentDocument) {
      throw new Error('Documento no encontrado');
    }

    // Crear una nueva versión con el contenido actual antes de restaurar
    await this.createVersion(
      documentId,
      currentDocument.content || '',
      currentDocument.googleDriveId || undefined,
      currentDocument.fileName || undefined,
      currentDocument.mimeType || undefined,
      `Respaldo antes de restaurar versión ${versionToRestore.version}`,
      restoredBy,
    );

    // Restaurar el contenido de la versión seleccionada
    await prisma.document.update({
      where: { id: documentId },
      data: {
        content: versionToRestore.content,
        googleDriveId: versionToRestore.googleDriveId,
        fileName: versionToRestore.fileName,
        mimeType: versionToRestore.mimeType,
        version: currentDocument.version + 1, // Incrementar versión
      },
    });
  }

  /**
   * Compara dos versiones de un documento
   */
  static async compareVersions(
    documentId: string,
    version1Id: string,
    version2Id: string,
  ): Promise<{
    version1: DocumentVersion;
    version2: DocumentVersion;
    differences: string[];
  }> {
    const [version1, version2] = await Promise.all([
      prisma.documentVersion.findUnique({ where: { id: version1Id } }),
      prisma.documentVersion.findUnique({ where: { id: version2Id } }),
    ]);

    if (
      !version1 ||
      !version2 ||
      version1.documentId !== documentId ||
      version2.documentId !== documentId
    ) {
      throw new Error('Versiones no encontradas o no pertenecen al documento');
    }

    // Análisis básico de diferencias (se puede mejorar con bibliotecas como diff)
    const differences = this.calculateDifferences(
      version1.content || '',
      version2.content || '',
    );

    return {
      version1: {
        id: version1.id,
        documentId: version1.documentId,
        version: version1.version,
        content: version1.content || undefined,
        changes: version1.changes || undefined,
        createdAt: version1.createdAt,
        createdBy: version1.createdBy,
      },
      version2: {
        id: version2.id,
        documentId: version2.documentId,
        version: version2.version,
        content: version2.content || undefined,
        changes: version2.changes || undefined,
        createdAt: version2.createdAt,
        createdBy: version2.createdBy,
      },
      differences,
    };
  }

  /**
   * Calcula diferencias básicas entre dos contenidos
   */
  private static calculateDifferences(
    content1: string,
    content2: string,
  ): string[] {
    const differences: string[] = [];

    // Análisis básico de longitud
    if (content1.length !== content2.length) {
      differences.push(
        `Diferencia en longitud: ${content1.length} vs ${content2.length} caracteres`,
      );
    }

    // Análisis de palabras
    const words1 = content1.split(/\s+/).filter((w) => w.length > 0);
    const words2 = content2.split(/\s+/).filter((w) => w.length > 0);

    if (words1.length !== words2.length) {
      differences.push(
        `Diferencia en palabras: ${words1.length} vs ${words2.length} palabras`,
      );
    }

    // Análisis de párrafos
    const paragraphs1 = content1
      .split(/\n\s*\n/)
      .filter((p) => p.trim().length > 0);
    const paragraphs2 = content2
      .split(/\n\s*\n/)
      .filter((p) => p.trim().length > 0);

    if (paragraphs1.length !== paragraphs2.length) {
      differences.push(
        `Diferencia en párrafos: ${paragraphs1.length} vs ${paragraphs2.length} párrafos`,
      );
    }

    return differences;
  }

  /**
   * Limpia versiones antiguas manteniendo solo las últimas N versiones
   */
  static async cleanupOldVersions(
    documentId: string,
    keepVersions: number = 10,
  ): Promise<number> {
    const versions = await prisma.documentVersion.findMany({
      where: { documentId },
      orderBy: { version: 'desc' },
      skip: keepVersions,
    });

    if (versions.length === 0) {
      return 0;
    }

    const versionIds = versions.map((v) => v.id);

    const deleteResult = await prisma.documentVersion.deleteMany({
      where: {
        id: { in: versionIds },
      },
    });

    return deleteResult.count;
  }

  /**
   * Obtiene estadísticas de versiones para un documento
   */
  static async getVersionStats(documentId: string): Promise<{
    totalVersions: number;
    oldestVersion: Date | null;
    newestVersion: Date | null;
    totalSize: number;
  }> {
    const versions = await prisma.documentVersion.findMany({
      where: { documentId },
      select: {
        createdAt: true,
        content: true,
        googleDriveId: true,
        fileName: true,
      },
    });

    const totalVersions = versions.length;
    const oldestVersion =
      versions.length > 0
        ? new Date(Math.min(...versions.map((v) => v.createdAt.getTime())))
        : null;
    const newestVersion =
      versions.length > 0
        ? new Date(Math.max(...versions.map((v) => v.createdAt.getTime())))
        : null;

    const totalSize = versions.reduce((size, version) => {
      const contentSize = version.content
        ? Buffer.byteLength(version.content, 'utf8')
        : 0;
      // Note: Google Drive files don't count towards local storage
      return size + contentSize;
    }, 0);

    return {
      totalVersions,
      oldestVersion,
      newestVersion,
      totalSize,
    };
  }
}
