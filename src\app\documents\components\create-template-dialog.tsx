'use client';

import { useState, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useServerAction } from 'zsa-react';
import { toast } from 'sonner';
import { Upload, X, Plus } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import {
  createDocumentTemplateSchema,
  CreateDocumentTemplateData,
  DocumentTemplate,
} from '@/features/document/schemas';
import { createDocumentTemplate } from '@/features/document/actions';

type PlaceholderType = NonNullable<
  CreateDocumentTemplateData['placeholders']
>[0];
import { DocumentGenerator } from '@/lib/document-generator';

interface CreateTemplateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onTemplateCreated: (template: DocumentTemplate) => void;
}

const categories = [
  'Admisión',
  'Notificación',
  'Suspensión',
  'Acuerdo',
  'Calificación',
  'General',
];

export function CreateTemplateDialog({
  open,
  onOpenChange,
  onTemplateCreated,
}: Readonly<CreateTemplateDialogProps>) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [extractedPlaceholders, setExtractedPlaceholders] = useState<
    PlaceholderType[]
  >([]);
  const [isExtracting, setIsExtracting] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const closeRef = useRef<HTMLButtonElement>(null);

  const { execute: executeCreate, isPending } = useServerAction(
    createDocumentTemplate,
    {
      onSuccess: ({ data }) => {
        toast.success('Plantilla creada exitosamente');
        onTemplateCreated(data);
        form.reset();
        setSelectedFile(null);
        setExtractedPlaceholders([]);
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        closeRef.current?.click();
      },
      onError: ({ err }) => {
        toast.error(err.message || 'Error al crear la plantilla');
      },
    },
  );

  const form = useForm({
    resolver: zodResolver(createDocumentTemplateSchema),
    defaultValues: {
      name: '',
      description: '',
      category: '',
      placeholders: [],
    },
  });

  const handleFileSelect = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.name.endsWith('.docx')) {
      alert('Por favor seleccione un archivo Word (.docx)');
      return;
    }

    setSelectedFile(file);
    setIsExtracting(true);

    try {
      // Extraer placeholders del archivo
      const placeholders = await DocumentGenerator.extractPlaceholders(file);
      setExtractedPlaceholders(placeholders);
      form.setValue('placeholders', placeholders);
    } catch (error) {
      console.error('Error extrayendo placeholders:', error);
      alert('Error al procesar el archivo Word');
    } finally {
      setIsExtracting(false);
    }
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
    setExtractedPlaceholders([]);
    form.setValue('placeholders', []);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const addCustomPlaceholder = () => {
    const currentPlaceholders = form.getValues('placeholders') || [];
    const newPlaceholder: PlaceholderType = {
      key: '',
      label: '',
      type: 'text',
      required: false,
    };
    form.setValue('placeholders', [...currentPlaceholders, newPlaceholder]);
  };

  const removePlaceholder = (index: number) => {
    const currentPlaceholders = form.getValues('placeholders') || [];
    form.setValue(
      'placeholders',
      currentPlaceholders.filter((_, i) => i !== index),
    );
  };

  const updatePlaceholder = (
    index: number,
    field: keyof PlaceholderType,
    value: string | boolean,
  ) => {
    const currentPlaceholders = form.getValues('placeholders') || [];
    const updatedPlaceholders = [...currentPlaceholders];
    updatedPlaceholders[index] = {
      ...updatedPlaceholders[index],
      [field]: value,
    };
    form.setValue('placeholders', updatedPlaceholders);
  };

  const onSubmit = async (data: CreateDocumentTemplateData) => {
    if (!selectedFile) {
      toast.error('Por favor seleccione un archivo Word');
      return;
    }

    executeCreate({
      name: data.name,
      description: data.description,
      category: data.category,
      placeholders: data.placeholders || [],
      file: selectedFile, // Pasar el archivo seleccionado
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Crear Nueva Plantilla</DialogTitle>
          <DialogDescription>
            Suba un archivo Word (.docx) para crear una nueva plantilla de
            documento
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Información básica */}
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nombre de la Plantilla</FormLabel>
                    <FormControl>
                      <Input placeholder="Ej: Auto de Admisión" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Categoría</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Seleccione una categoría" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Descripción</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Descripción de la plantilla y su uso"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Subida de archivo */}
            <div className="space-y-4">
              <FormLabel>Archivo Word (.docx)</FormLabel>

              {!selectedFile ? (
                <div className="rounded-lg border-2 border-dashed border-gray-300 p-6 text-center">
                  <Upload className="mx-auto h-12 w-12 text-gray-400" />
                  <div className="mt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => fileInputRef.current?.click()}
                    >
                      Seleccionar Archivo Word
                    </Button>
                    <p className="mt-2 text-sm text-gray-500">
                      Solo archivos .docx son permitidos
                    </p>
                  </div>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".docx"
                    onChange={handleFileSelect}
                    className="hidden"
                  />
                </div>
              ) : (
                <div className="flex items-center justify-between rounded-lg border p-4">
                  <div className="flex items-center space-x-3">
                    <div className="rounded bg-blue-100 p-2">
                      <Upload className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-medium">{selectedFile.name}</p>
                      <p className="text-sm text-gray-500">
                        {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={handleRemoveFile}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>

            {/* Placeholders extraídos */}
            {extractedPlaceholders.length > 0 && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <FormLabel>Placeholders Detectados</FormLabel>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addCustomPlaceholder}
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Agregar Campo
                  </Button>
                </div>

                <div className="space-y-3">
                  {(form.watch('placeholders') || []).map(
                    (placeholder, index) => (
                      <div
                        key={index}
                        className="grid grid-cols-5 items-end gap-3"
                      >
                        <div>
                          <label className="text-sm font-medium">Clave</label>
                          <Input
                            value={placeholder.key}
                            onChange={(e) =>
                              updatePlaceholder(index, 'key', e.target.value)
                            }
                            placeholder="{{clave}}"
                          />
                        </div>
                        <div>
                          <label className="text-sm font-medium">
                            Etiqueta
                          </label>
                          <Input
                            value={placeholder.label}
                            onChange={(e) =>
                              updatePlaceholder(index, 'label', e.target.value)
                            }
                            placeholder="Nombre visible"
                          />
                        </div>
                        <div>
                          <label className="text-sm font-medium">Tipo</label>
                          <Select
                            value={placeholder.type}
                            onValueChange={(value) =>
                              updatePlaceholder(index, 'type', value)
                            }
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="text">Texto</SelectItem>
                              <SelectItem value="number">Número</SelectItem>
                              <SelectItem value="date">Fecha</SelectItem>
                              <SelectItem value="boolean">Sí/No</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={placeholder.required}
                            onChange={(e) =>
                              updatePlaceholder(
                                index,
                                'required',
                                e.target.checked,
                              )
                            }
                          />
                          <label className="text-sm">Requerido</label>
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removePlaceholder(index)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ),
                  )}
                </div>
              </div>
            )}

            {isExtracting && (
              <div className="py-4 text-center">
                <p className="text-sm text-gray-500">
                  Extrayendo placeholders del documento...
                </p>
              </div>
            )}

            {/* Botones */}
            <div className="flex justify-end space-x-3">
              <DialogClose asChild>
                <Button
                  type="button"
                  variant="outline"
                  ref={closeRef}
                  disabled={isPending}
                >
                  Cancelar
                </Button>
              </DialogClose>
              <Button
                type="submit"
                disabled={!selectedFile || isExtracting || isPending}
              >
                {isPending ? 'Creando...' : 'Crear Plantilla'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
